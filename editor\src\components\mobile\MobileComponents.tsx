/**
 * MobileComponents.tsx
 * 
 * 移动端特定组件集合
 */

import React, { useState, useRef, useEffect } from 'react';
import { Input, List, Drawer, Modal } from 'antd';
import {
  // MenuOutlined,
  SearchOutlined,
  MoreOutlined,
  ArrowLeftOutlined,
  // ShareAltOutlined,
  // HeartOutlined,
  // StarOutlined
} from '@ant-design/icons';
import './MobileComponents.module.css';

/**
 * 移动端导航栏
 */
export interface MobileNavBarProps {
  title?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onLeftClick?: () => void;
  onRightClick?: () => void;
  backgroundColor?: string;
  textColor?: string;
  fixed?: boolean;
}

export const MobileNavBar: React.FC<MobileNavBarProps> = ({
  title = '标题',
  leftIcon = <ArrowLeftOutlined />,
  rightIcon = <MoreOutlined />,
  onLeftClick,
  onRightClick,
  backgroundColor = '#ffffff',
  textColor = '#000000',
  fixed = true
}) => {
  return (
    <div 
      className={`mobile-navbar ${fixed ? 'fixed' : ''}`}
      style={{ backgroundColor, color: textColor }}
    >
      <div className="navbar-left" onClick={onLeftClick}>
        {leftIcon}
      </div>
      <div className="navbar-title">
        {title}
      </div>
      <div className="navbar-right" onClick={onRightClick}>
        {rightIcon}
      </div>
    </div>
  );
};

/**
 * 移动端底部导航
 */
export interface MobileTabBarProps {
  tabs: Array<{
    key: string;
    title: string;
    icon: React.ReactNode;
    badge?: number;
  }>;
  activeKey?: string;
  onChange?: (key: string) => void;
  backgroundColor?: string;
  activeColor?: string;
  inactiveColor?: string;
}

export const MobileTabBar: React.FC<MobileTabBarProps> = ({
  tabs,
  activeKey,
  onChange,
  backgroundColor = '#ffffff',
  activeColor = '#1890ff',
  inactiveColor = '#8c8c8c'
}) => {
  return (
    <div className="mobile-tabbar" style={{ backgroundColor }}>
      {tabs.map(tab => (
        <div
          key={tab.key}
          className={`tabbar-item ${activeKey === tab.key ? 'active' : ''}`}
          onClick={() => onChange?.(tab.key)}
          style={{ 
            color: activeKey === tab.key ? activeColor : inactiveColor 
          }}
        >
          <div className="tabbar-icon">
            {tab.icon}
            {tab.badge && tab.badge > 0 && (
              <span className="tabbar-badge">{tab.badge}</span>
            )}
          </div>
          <div className="tabbar-title">{tab.title}</div>
        </div>
      ))}
    </div>
  );
};

/**
 * 移动端卡片
 */
export interface MobileCardProps {
  title?: string;
  subtitle?: string;
  image?: string;
  content?: React.ReactNode;
  actions?: React.ReactNode[];
  onClick?: () => void;
  style?: React.CSSProperties;
}

export const MobileCard: React.FC<MobileCardProps> = ({
  title,
  subtitle,
  image,
  content,
  actions,
  onClick,
  style
}) => {
  return (
    <div className="mobile-card" onClick={onClick} style={style}>
      {image && (
        <div className="card-image">
          <img src={image} alt={title} />
        </div>
      )}
      <div className="card-content">
        {title && <div className="card-title">{title}</div>}
        {subtitle && <div className="card-subtitle">{subtitle}</div>}
        {content && <div className="card-body">{content}</div>}
        {actions && actions.length > 0 && (
          <div className="card-actions">
            {actions.map((action, index) => (
              <div key={index} className="card-action">
                {action}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * 移动端列表项
 */
export interface MobileListItemProps {
  title: string;
  subtitle?: string;
  description?: string;
  avatar?: React.ReactNode;
  extra?: React.ReactNode;
  arrow?: boolean;
  onClick?: () => void;
}

export const MobileListItem: React.FC<MobileListItemProps> = ({
  title,
  subtitle,
  description,
  avatar,
  extra,
  arrow = false,
  onClick
}) => {
  return (
    <div className="mobile-list-item" onClick={onClick}>
      {avatar && (
        <div className="list-item-avatar">
          {avatar}
        </div>
      )}
      <div className="list-item-content">
        <div className="list-item-title">{title}</div>
        {subtitle && <div className="list-item-subtitle">{subtitle}</div>}
        {description && <div className="list-item-description">{description}</div>}
      </div>
      {extra && (
        <div className="list-item-extra">
          {extra}
        </div>
      )}
      {arrow && (
        <div className="list-item-arrow">
          <ArrowLeftOutlined style={{ transform: 'rotate(180deg)' }} />
        </div>
      )}
    </div>
  );
};

/**
 * 移动端搜索栏
 */
export interface MobileSearchBarProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onSearch?: (value: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  showCancel?: boolean;
  onCancel?: () => void;
}

export const MobileSearchBar: React.FC<MobileSearchBarProps> = ({
  placeholder = '搜索',
  value,
  onChange,
  onSearch,
  onFocus,
  onBlur,
  showCancel = false,
  onCancel
}) => {
  const [focused, setFocused] = useState(false);
  const [inputValue, setInputValue] = useState(value || '');

  const handleFocus = () => {
    setFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    setFocused(false);
    onBlur?.();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    onChange?.(newValue);
  };

  const handleSearch = () => {
    onSearch?.(inputValue);
  };

  const handleCancel = () => {
    setInputValue('');
    setFocused(false);
    onChange?.('');
    onCancel?.();
  };

  return (
    <div className={`mobile-search-bar ${focused ? 'focused' : ''}`}>
      <div className="search-input-wrapper">
        <SearchOutlined className="search-icon" />
        <input
          type="text"
          className="search-input"
          placeholder={placeholder}
          value={inputValue}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
        />
      </div>
      {(showCancel || focused) && (
        <button className="search-cancel" onClick={handleCancel}>
          取消
        </button>
      )}
    </div>
  );
};

/**
 * 移动端滑动操作
 */
export interface MobileSwipeActionProps {
  children: React.ReactNode;
  leftActions?: Array<{
    text: string;
    color?: string;
    onClick: () => void;
  }>;
  rightActions?: Array<{
    text: string;
    color?: string;
    onClick: () => void;
  }>;
}

export const MobileSwipeAction: React.FC<MobileSwipeActionProps> = ({
  children,
  leftActions = [],
  rightActions = []
}) => {
  const [swipeOffset, setSwipeOffset] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const startX = useRef(0);
  const currentX = useRef(0);

  const handleTouchStart = (e: React.TouchEvent) => {
    startX.current = e.touches[0].clientX;
    setIsDragging(true);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return;
    
    currentX.current = e.touches[0].clientX;
    const diff = currentX.current - startX.current;
    setSwipeOffset(diff);
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
    
    const threshold = 60;
    if (Math.abs(swipeOffset) > threshold) {
      // 保持滑动状态
      setSwipeOffset(swipeOffset > 0 ? 100 : -100);
    } else {
      // 回弹
      setSwipeOffset(0);
    }
  };

  const handleActionClick = (action: any) => {
    action.onClick();
    setSwipeOffset(0);
  };

  return (
    <div className="mobile-swipe-action">
      {leftActions.length > 0 && (
        <div className="swipe-actions left">
          {leftActions.map((action, index) => (
            <button
              key={index}
              className="swipe-action-button"
              style={{ backgroundColor: action.color || '#52c41a' }}
              onClick={() => handleActionClick(action)}
            >
              {action.text}
            </button>
          ))}
        </div>
      )}
      
      <div
        className="swipe-content"
        style={{ transform: `translateX(${swipeOffset}px)` }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {children}
      </div>
      
      {rightActions.length > 0 && (
        <div className="swipe-actions right">
          {rightActions.map((action, index) => (
            <button
              key={index}
              className="swipe-action-button"
              style={{ backgroundColor: action.color || '#ff4d4f' }}
              onClick={() => handleActionClick(action)}
            >
              {action.text}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

/**
 * 移动端下拉刷新
 */
export interface MobilePullRefreshProps {
  children: React.ReactNode;
  onRefresh: () => Promise<void>;
  refreshing?: boolean;
  threshold?: number;
}

export const MobilePullRefresh: React.FC<MobilePullRefreshProps> = ({
  children,
  onRefresh,
  refreshing = false,
  threshold = 60
}) => {
  const [pullDistance, setPullDistance] = useState(0);
  const [, setIsPulling] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(refreshing);
  const startY = useRef(0);
  const scrollTop = useRef(0);

  const handleTouchStart = (e: React.TouchEvent) => {
    startY.current = e.touches[0].clientY;
    scrollTop.current = (e.currentTarget as HTMLElement).scrollTop;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (scrollTop.current > 0) return;
    
    const currentY = e.touches[0].clientY;
    const diff = currentY - startY.current;
    
    if (diff > 0) {
      setIsPulling(true);
      setPullDistance(Math.min(diff * 0.5, threshold * 1.5));
    }
  };

  const handleTouchEnd = async () => {
    if (pullDistance >= threshold && !isRefreshing) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } finally {
        setIsRefreshing(false);
      }
    }
    
    setIsPulling(false);
    setPullDistance(0);
  };

  useEffect(() => {
    setIsRefreshing(refreshing);
  }, [refreshing]);

  return (
    <div
      className="mobile-pull-refresh"
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      <div 
        className="pull-refresh-indicator"
        style={{ 
          height: pullDistance,
          opacity: pullDistance / threshold 
        }}
      >
        {isRefreshing ? (
          <div className="refresh-loading">刷新中...</div>
        ) : pullDistance >= threshold ? (
          <div className="refresh-ready">释放刷新</div>
        ) : (
          <div className="refresh-pull">下拉刷新</div>
        )}
      </div>
      
      <div 
        className="pull-refresh-content"
        style={{ transform: `translateY(${pullDistance}px)` }}
      >
        {children}
      </div>
    </div>
  );
};

/**
 * 移动端操作表
 */
export interface MobileActionSheetProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  actions: Array<{
    text: string;
    color?: string;
    disabled?: boolean;
    onClick: () => void;
  }>;
  cancelText?: string;
}

export const MobileActionSheet: React.FC<MobileActionSheetProps> = ({
  visible,
  onClose,
  title,
  actions,
  cancelText = '取消'
}) => {
  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      closable={false}
      className="mobile-action-sheet"
      style={{ bottom: 0, margin: 0 }}
      bodyStyle={{ padding: 0 }}
    >
      <div className="action-sheet-content">
        {title && (
          <div className="action-sheet-title">{title}</div>
        )}
        
        <div className="action-sheet-actions">
          {actions.map((action, index) => (
            <button
              key={index}
              className={`action-sheet-button ${action.disabled ? 'disabled' : ''}`}
              style={{ color: action.color }}
              disabled={action.disabled}
              onClick={() => {
                action.onClick();
                onClose();
              }}
            >
              {action.text}
            </button>
          ))}
        </div>
        
        <div className="action-sheet-cancel">
          <button className="action-sheet-button" onClick={onClose}>
            {cancelText}
          </button>
        </div>
      </div>
    </Modal>
  );
};
